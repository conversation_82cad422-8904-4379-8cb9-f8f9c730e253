import config from '@payload-config';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { lexicalToText, lexicalToHTML } from '@/lib/utils/lexical';
import { translateToGerman } from '@/lib/integrations/openai/german-translation';

export async function POST(request: NextRequest) {
  try {
    const { articleId, currentEnglishContent } = await request.json();

    if (!articleId) {
      console.error('❌ No articleId provided in request');
      return NextResponse.json(
        { success: false, error: 'Article ID is required' },
        { status: 400 }
      );
    }

    console.log(`🚀 Starting German translation for article: ${articleId}`);
    console.log(
      '📋 Using enhanced Lexical→HTML content extraction to preserve formatting'
    );

    // Check if we received current form data
    if (currentEnglishContent) {
      console.log(
        '💾 Current form data received - will save changes before translating'
      );
      console.log('📊 Current English content:', {
        hasTitle: !!currentEnglishContent.title,
        hasContent: !!currentEnglishContent.content,
        contentLength: currentEnglishContent.content
          ? JSON.stringify(currentEnglishContent.content).length
          : 0,
        hasKeyInsights: currentEnglishContent.keyInsights?.length > 0,
        hasKeywords: currentEnglishContent.keywords?.length > 0,
      });
    }

    const payload = await getPayload({ config });

    // Fetch the article to get existing data and validate eligibility
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    });

    if (!article) {
      console.error(`❌ Article not found with ID: ${articleId}`);
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }

    console.log(`📄 Article found: ${article.title}`);
    console.log(`📊 Article workflow stage: ${article.workflowStage}`);
    console.log(`📊 Article type: ${article.articleType}`);

    // Validate article is eligible for translation (both generated and curated articles)
    if (!['generated', 'curated'].includes(article.articleType)) {
      console.error(`❌ Article type not eligible: ${article.articleType}`);
      return NextResponse.json(
        {
          success: false,
          error: 'Article type must be generated or curated for translation',
        },
        { status: 400 }
      );
    }

    // If we have current form data, save it first before translating
    if (currentEnglishContent && currentEnglishContent.content) {
      console.log('💾 Saving current form changes before translation...');

      try {
        // Prepare update data with current form content
        const saveUpdateData: any = {};

        // Update English tab with current form data
        if (currentEnglishContent.title || currentEnglishContent.content) {
          saveUpdateData.englishTab = {
            ...article.englishTab, // Preserve existing English tab data
            ...(currentEnglishContent.title && {
              enhancedTitle: currentEnglishContent.title,
            }),
            ...(currentEnglishContent.summary && {
              enhancedSummary: currentEnglishContent.summary,
            }),
            ...(currentEnglishContent.content && {
              enhancedContent: currentEnglishContent.content,
            }),
            ...(currentEnglishContent.keyInsights && {
              enhancedKeyInsights: currentEnglishContent.keyInsights,
            }),
            ...(currentEnglishContent.keywords && {
              keywords: currentEnglishContent.keywords,
            }),
          };
        }

        // Preserve all other existing fields
        if (article.featuredImage) {
          saveUpdateData.featuredImage = article.featuredImage;
        }
        if (article.categories && article.categories.length > 0) {
          saveUpdateData.categories = article.categories;
        }
        if (article.placement) {
          saveUpdateData.placement = article.placement;
        }
        if (article.pinned !== undefined) {
          saveUpdateData.pinned = article.pinned;
        }
        if (article.trending !== undefined) {
          saveUpdateData.trending = article.trending;
        }
        if (article.relatedCompanies && article.relatedCompanies.length > 0) {
          saveUpdateData.relatedCompanies = article.relatedCompanies;
        }
        if (article.hasGermanTranslation && article.germanTab) {
          saveUpdateData.hasGermanTranslation = article.hasGermanTranslation;
          saveUpdateData.germanTab = article.germanTab;
        }

        // Save current changes to database
        await payload.update({
          collection: 'articles',
          id: articleId,
          data: saveUpdateData,
        });

        console.log('✅ Current form changes saved successfully');

        // Update our local article object with the saved changes for translation
        article.englishTab = saveUpdateData.englishTab;
      } catch (saveError) {
        console.error('❌ Error saving current changes:', saveError);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to save current changes before translation',
          },
          { status: 500 }
        );
      }
    }

    // Validate article has English content (enhanced or basic)
    // For curated articles, content might be in main title field initially
    const hasEnhancedContent =
      article.englishTab?.enhancedTitle && article.englishTab?.enhancedContent;
    const hasBasicContent =
      article.title &&
      (currentEnglishContent?.content || article.englishTab?.enhancedContent);

    if (!hasEnhancedContent && !hasBasicContent) {
      console.error('❌ Article missing English content for translation');
      console.log('  Enhanced title:', !!article.englishTab?.enhancedTitle);
      console.log('  Enhanced content:', !!article.englishTab?.enhancedContent);
      console.log('  Basic title:', !!article.title);
      console.log('  Current content:', !!currentEnglishContent?.content);
      return NextResponse.json(
        {
          success: false,
          error:
            'Article must have English title and content before translation',
        },
        { status: 400 }
      );
    }

    // Allow re-translation if German translation already exists
    const isReTranslation = !!article.germanTab?.germanTitle;
    if (isReTranslation) {
      console.log('🔄 Re-translating existing German content');
      console.log('  Previous German title:', article.germanTab?.germanTitle);
      console.log('  Will translate from current English content');
    } else {
      console.log('🆕 Initial German translation');
    }

    // Extract English content for translation
    // Use current form data if provided (for real-time translation), otherwise use database data
    let englishTitle,
      englishSummary,
      englishContentText,
      englishKeyInsights,
      englishKeywords;

    if (currentEnglishContent) {
      console.log(
        '🔄 Using current form data for translation (includes unsaved changes)'
      );

      // For curated articles, title might be in main title field or enhanced title
      englishTitle = currentEnglishContent.title || article.title;
      englishSummary = currentEnglishContent.summary || '';
      englishContentText = currentEnglishContent.content
        ? lexicalToHTML(currentEnglishContent.content)
        : '';
      englishKeyInsights = currentEnglishContent.keyInsights
        ? currentEnglishContent.keyInsights.map((item: any) =>
            typeof item === 'string' ? item : item.insight
          )
        : [];
      englishKeywords = currentEnglishContent.keywords
        ? currentEnglishContent.keywords.map((item: any) =>
            typeof item === 'string' ? item : item.keyword
          )
        : [];
    } else {
      console.log('📄 Using database data for translation');

      // Fallback to main title if enhanced title not available (for curated articles)
      englishTitle = article.englishTab?.enhancedTitle || article.title;
      englishSummary = article.englishTab?.enhancedSummary || '';
      englishContentText = article.englishTab?.enhancedContent
        ? lexicalToHTML(article.englishTab.enhancedContent)
        : '';
      englishKeyInsights = article.englishTab?.enhancedKeyInsights
        ? article.englishTab.enhancedKeyInsights.map(
            (item: any) => item.insight
          )
        : [];
      englishKeywords =
        article.englishTab?.keywords?.map((item: any) => item.keyword) || [];
    }

    // Validate we have the required content for translation
    if (!englishTitle || !englishContentText) {
      console.error('❌ Missing required English content for translation');
      console.log('  Title present:', !!englishTitle);
      console.log('  Content present:', !!englishContentText);
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required English title or content for translation',
        },
        { status: 400 }
      );
    }

    console.log('📄 Processing English content for translation:');
    console.log(
      '  Source:',
      currentEnglishContent ? 'Current form data' : 'Database'
    );
    console.log('  Title:', englishTitle);
    console.log('  Content length:', englishContentText.length, 'characters');
    console.log('  Key insights:', englishKeyInsights.length, 'insights');
    console.log('  Keywords:', englishKeywords.length, 'keywords');

    // Log structure preservation info
    if (englishContentText.length > 0) {
      const contentSample = englishContentText.substring(0, 200);
      const hasHtmlTags = /<[^>]+>/.test(contentSample);
      console.log('🔍 Content structure analysis:');
      console.log(
        '  Format:',
        hasHtmlTags ? 'HTML (structure preserved)' : 'Plain text'
      );
      console.log('  Sample:', contentSample + '...');
      if (hasHtmlTags) {
        const listCount = (englishContentText.match(/<li>/g) || []).length;
        const tableCount = (englishContentText.match(/<table>/g) || []).length;
        const headingCount = (englishContentText.match(/<h[1-6]>/g) || [])
          .length;
        console.log('  Complex elements:', {
          lists: listCount,
          tables: tableCount,
          headings: headingCount,
        });
      }
    }

    // Perform German translation
    const translationResult = await translateToGerman(
      {
        title: englishTitle,
        summary: englishSummary,
        content: englishContentText,
        keyInsights: englishKeyInsights,
        keywords: englishKeywords,
      },
      {
        temperature: 0.3, // Lower temperature for more literal translations
        includeProcessingMetadata: true,
      }
    );

    if (!translationResult.success || !translationResult.data) {
      console.error('❌ German translation failed:', translationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: `German translation failed: ${translationResult.error}`,
        },
        { status: 500 }
      );
    }

    console.log('✅ German translation successful');
    console.log(
      `📊 Performance: ${translationResult.metrics.processingTime}ms`
    );

    // Extract translated German content
    const translatedData = translationResult.data;
    const germanTitle = translatedData.germanTitle;
    const germanSummary = translatedData.germanSummary;
    const germanContent = translatedData.germanContent;
    const germanKeyInsights = translatedData.germanKeyInsights;
    const germanKeywords = translatedData.germanKeywords;

    console.log('📝 German translation parsing:');
    console.log('  Title:', germanTitle);
    console.log('  Content length:', germanContent.length);
    console.log('  Key insights:', germanKeyInsights.length, 'insights');
    console.log('  Keywords:', germanKeywords.length, 'keywords');

    // Convert HTML content to Lexical format
    console.log('🔄 Converting German content to Lexical format...');
    const htmlResult = await htmlToLexical(germanContent);

    if (!htmlResult.metrics.success) {
      console.error('❌ HTML to Lexical conversion failed:', htmlResult);
      return NextResponse.json(
        {
          success: false,
          error: 'HTML to Lexical conversion failed',
          details: htmlResult.metrics,
        },
        { status: 500 }
      );
    }

    const germanContentLexical = htmlResult.result;

    // Strip HTML formatting from title
    const stripHtml = (text: string): string => {
      return text
        .replace(/<[^>]*>/g, '') // Remove all HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&') // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    const cleanGermanTitle = stripHtml(germanTitle);
    console.log('🧹 Title cleaning:', germanTitle, '→', cleanGermanTitle);

    // Prepare update data with German translated content
    const updateData: any = {
      hasGermanTranslation: true, // Set flag to show German tab
      germanTab: {
        germanTitle: cleanGermanTitle,
        germanContent: germanContentLexical,
        germanSummary: germanSummary,
        germanKeyInsights: germanKeyInsights.map((insight: string) => ({
          insight,
        })),
        germanKeywords: germanKeywords.map((keyword: string) => ({
          keyword,
        })),
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
    };

    // Preserve important existing fields during translation
    if (article.featuredImage) {
      updateData.featuredImage = article.featuredImage;
    }

    // Preserve other metadata fields that shouldn't be lost during translation
    if (article.categories && article.categories.length > 0) {
      updateData.categories = article.categories;
    }

    if (article.placement) {
      updateData.placement = article.placement;
    }

    if (article.pinned !== undefined) {
      updateData.pinned = article.pinned;
    }

    if (article.trending !== undefined) {
      updateData.trending = article.trending;
    }

    if (article.relatedCompanies && article.relatedCompanies.length > 0) {
      updateData.relatedCompanies = article.relatedCompanies;
    }

    // Update workflow stage to "translated" if it's currently "candidate-article"
    if (article.workflowStage === 'candidate-article') {
      updateData.workflowStage = 'translated';
    }

    // Update the article
    const updatedArticle = await payload.update({
      collection: 'articles',
      id: articleId,
      data: updateData,
    });

    console.log(
      `✅ German ${isReTranslation ? 're-' : ''}translation completed for article ${articleId}`
    );

    if (isReTranslation) {
      console.log('🔄 Re-translation summary:');
      console.log('  New German title:', cleanGermanTitle);
      console.log('  Content length:', germanContent.length, 'characters');
      console.log(
        '  Processing time:',
        translationResult.metrics.processingTime,
        'ms'
      );
    }

    return NextResponse.json({
      success: true,
      message: `Article ${isReTranslation ? 're-' : ''}translated to German successfully`,
      translatedContent: {
        germanTitle: cleanGermanTitle,
        germanContent: germanContentLexical,
        germanSummary: germanSummary,
        germanKeyInsights: germanKeyInsights,
        germanKeywords: germanKeywords,
      },
      metrics: {
        processingTime: translationResult.metrics.processingTime,
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
      article: updatedArticle,
    });
  } catch (error: any) {
    console.error('❌ Error translating article:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Unknown error occurred during translation',
      },
      { status: 500 }
    );
  }
}
