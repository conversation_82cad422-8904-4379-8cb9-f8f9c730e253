/**
 * Article Document Controls Component
 *
 * Provides translation functionality for candidate articles with proper UI updates.
 *
 * Key Features:
 * - Translates enhanced English content to German using OpenAI
 * - Updates form fields programmatically using PayloadCMS patterns
 * - Forces page refresh to ensure UI reflects changes immediately
 * - Provides visual feedback during translation process
 * - <PERSON>les re-translation scenarios
 *
 * Solution for UI Update Issue:
 * - Uses dispatchFields() to update form state
 * - Implements router.refresh() to force page re-render
 * - Provides immediate visual feedback with button state changes
 * - Includes fallback option for window.location.reload() if needed
 *
 * <AUTHOR> Blick Development Team
 * @updated 2025-01-16 - Fixed UI refresh issue after translation
 */
'use client';

import React, { useState, useCallback } from 'react';
import { useDocumentInfo, useAllFormFields } from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import { lexicalToText } from '../../../lib/utils/lexical-text';

// Temporary toast implementation - will be replaced with proper PayloadCMS notifications
const useToast = () => {
  return {
    toast: {
      error: (message: string, options?: any) =>
        console.error('Toast Error:', message, options),
      success: (message: string, options?: any) =>
        console.log('Toast Success:', message, options),
    },
  };
};

// Helper to reduce fields to values - basic implementation
const reduceFieldsToValues = (fields: any, includeInternalFields: boolean) => {
  const values: any = {};
  Object.keys(fields).forEach(key => {
    if (
      fields[key] &&
      typeof fields[key] === 'object' &&
      'value' in fields[key]
    ) {
      values[key] = fields[key].value;
    }
  });
  return values;
};

export const ArticleDocumentControls = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  const { toast } = useToast();
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);

  // Enhancement state management - follows same pattern as translation
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementJustCompleted, setEnhancementJustCompleted] =
    useState(false);

  // Convert fields to data using PayloadCMS helper
  const data = reduceFieldsToValues(fields, true);
  const workflowStage = data?.workflowStage;
  const articleType = data?.articleType;
  const hasGermanTranslation =
    data?.germanTab?.germanTitle && data?.germanTab?.germanContent;

  // Simple field validation for ALL required fields (50+ characters each)
  const validateRequiredFields = () => {
    const title = data?.title || '';
    const enhancedTitle =
      data?.['englishTab.enhancedTitle'] ||
      fields?.['englishTab.enhancedTitle']?.value ||
      '';
    const enhancedSummary =
      data?.['englishTab.enhancedSummary'] ||
      fields?.['englishTab.enhancedSummary']?.value ||
      '';
    const enhancedContent =
      data?.['englishTab.enhancedContent'] ||
      fields?.['englishTab.enhancedContent']?.value;

    // Convert Lexical content to text for length check (only for content field)
    let contentText = '';
    if (enhancedContent && typeof enhancedContent === 'object') {
      contentText = lexicalToText(enhancedContent);
    }

    return {
      title: title.length >= 50,
      enhancedTitle: enhancedTitle.length >= 50,
      enhancedSummary: enhancedSummary.length >= 50,
      enhancedContent: contentText.length >= 50,
    };
  };

  const validation = validateRequiredFields();
  const allFieldsValid = Object.values(validation).every(v => v);

  // Simple detection: Has been enhanced flag = already enhanced by AI (same pattern as translation)
  const isEnhanced = !!(data?.hasBeenEnhanced || enhancementJustCompleted);

  // Clear validation messages for specific fields
  const getValidationMessage = useCallback(() => {
    if (!id) return 'Please save the article first';
    if (!validation.title) return 'Title needs at least 50 characters';
    if (!validation.enhancedTitle)
      return 'Enhanced English Title needs at least 50 characters';
    if (!validation.enhancedSummary)
      return 'Enhanced English Summary needs at least 50 characters';
    if (!validation.enhancedContent)
      return 'Enhanced English Content needs at least 50 characters';
    return null;
  }, [id, validation]);

  // Translation button logic:
  // For curated articles: Can translate when enhanced fields meet validation (50+ chars each)
  // For generated articles: Must be AI-enhanced first
  const canTranslate =
    articleType === 'curated'
      ? // Curated: Use same validation as enhance button (50+ chars each field)
        !!id && // Must be saved first
        allFieldsValid && // All enhanced fields have 50+ characters
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage)
      : // Generated: Must be AI-enhanced first
        isEnhanced &&
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage);

  const canEnhance =
    !!id && // Must be saved first
    articleType === 'curated' &&
    ['curated-draft', 'candidate-article'].includes(workflowStage) && // Allow re-enhancement like re-translation
    allFieldsValid &&
    !isEnhancing &&
    !isTranslating; // Prevent simultaneous operations

  // Show enhance button logic:
  // - Generated articles: Show immediately if in appropriate stages (already saved)
  // - Curated articles: Show after saved (consistent with translate button)
  const showEnhanceButton =
    articleType === 'curated'
      ? !!id && ['curated-draft', 'candidate-article'].includes(workflowStage) // Curated: show after saved
      : ['curated-draft', 'candidate-article'].includes(workflowStage); // Generated: show immediately

  // Show translation button logic:
  // - Curated articles: Show after saved (same as enhance button for consistency)
  // - Generated articles: Show when AI-enhanced
  const showTranslationButton =
    articleType === 'curated'
      ? !!id &&
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage) // Curated: show after saved
      : canTranslate; // Generated: show when enhanced

  // Debug logging - after all variables are declared
  React.useEffect(() => {
    console.log('🔍 Enhancement state debug:', {
      hasBeenEnhanced: data?.hasBeenEnhanced,
      hasGermanTranslation: data?.hasGermanTranslation,
      allFieldsValid, // Validation for all required fields
      enhancementJustCompleted,
      isEnhanced, // AI enhancement flag
      workflowStage,
      articleType,
      canTranslate, // Updated logic
      showTranslationButton,
      showEnhanceButton,
    });
  }, [
    data?.hasBeenEnhanced,
    data?.hasGermanTranslation,
    allFieldsValid,
    enhancementJustCompleted,
    isEnhanced,
    workflowStage,
    articleType,
    canTranslate,
    showTranslationButton,
    showEnhanceButton,
  ]);

  // Define the translation handler (must be before any conditional returns)
  const handleTranslateToGerman = useCallback(async () => {
    if (!id) {
      toast.error('No article ID found');
      return;
    }

    // Validation logic based on article type
    if (articleType === 'generated' && !isEnhanced) {
      toast.error('Generated articles must be AI-enhanced before translation');
      return;
    }

    if (articleType === 'curated' && !allFieldsValid) {
      const validationMessage = getValidationMessage();
      toast.error(
        validationMessage ||
          'Please complete all required fields before translation'
      );
      return;
    }

    setIsTranslating(true);

    try {
      // Get current form data (includes any unsaved changes)
      const currentFormData = reduceFieldsToValues(fields, true);

      // Extract current English content from form state
      const currentEnglishContent = {
        title: currentFormData?.englishTab?.enhancedTitle,
        summary: currentFormData?.englishTab?.enhancedSummary || '',
        content: currentFormData?.englishTab?.enhancedContent,
        keyInsights: currentFormData?.englishTab?.enhancedKeyInsights || [],
        keywords: currentFormData?.englishTab?.keywords || [],
      };

      console.log('🔄 Translating current form data (not database data)');
      console.log('📄 Current English content:', {
        title: currentEnglishContent.title,
        contentLength: currentEnglishContent.content
          ? JSON.stringify(currentEnglishContent.content).length
          : 0,
        hasKeyInsights: currentEnglishContent.keyInsights.length > 0,
        hasKeywords: currentEnglishContent.keywords.length > 0,
      });

      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: id,
          currentEnglishContent: currentEnglishContent, // Pass current form data
        }),
      });

      const result = await response.json();

      if (result.success && result.translatedContent) {
        console.log('✅ Translation successful');
        console.log('Translated content:', result.translatedContent);

        // Show success feedback to user
        toast.success('German translation completed successfully!', {
          description: hasGermanTranslation
            ? 'Article has been re-translated to German.'
            : 'Article has been translated to German.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns
        // The API response contains the translated content
        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: result.translatedContent.germanTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanContent',
          value: result.translatedContent.germanContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanSummary',
          value: result.translatedContent.germanSummary,
        });

        // Update additional fields if they exist in the response
        if (result.translatedContent.germanKeyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeyInsights',
            value: result.translatedContent.germanKeyInsights,
          });
        }

        if (result.translatedContent.germanKeywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeywords',
            value: result.translatedContent.germanKeywords,
          });
        }

        dispatchFields({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });

        // Update workflow stage to 'translated'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'translated',
        });

        console.log('✅ Form fields updated successfully');

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Log the current form state for debugging
        console.log('📊 Current form state after field updates:', {
          germanTitle:
            result.translatedContent.germanTitle?.substring(0, 50) + '...',
          workflowStage: 'translated',
          hasGermanTranslation: true,
          fieldsUpdated: Object.keys(result.translatedContent).length,
        });

        // Force form re-render and page refresh using PayloadCMS best practices
        // This ensures the UI reflects the updated state immediately
        setTimeout(() => {
          // Method 1: Use router.refresh() for Next.js App Router (preferred)
          router.refresh();

          // Method 2: Alternative fallback - uncomment if router.refresh() doesn't work
          // This will force a full page reload to ensure all content is updated
          // window.location.reload();
        }, 1500); // Delay to allow toast to show and field updates to process

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);
      } else {
        console.error('❌ Translation failed:', result.error);
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      console.error('❌ Translation error:', error);
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  }, [
    id,
    isEnhanced,
    hasGermanTranslation,
    allFieldsValid,
    articleType,
    getValidationMessage,
    fields,
    dispatchFields,
    toast,
    router,
  ]);

  // Enhancement handler - for curated articles
  const handleEnhance = useCallback(async () => {
    console.log('🚀 Enhancement started for article ID:', id);

    if (!id) {
      toast.error('Please save the article first before enhancing', {
        description:
          'You need to save this article before it can be enhanced by AI.',
      });
      return;
    }

    if (!allFieldsValid) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Please fill in all required fields');
      return;
    }

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: id }),
      });

      const result = await response.json();

      console.log('🔍 API Response structure:', {
        success: result.success,
        hasEnhancedContent: !!result.enhancedContent,
        enhancedContentKeys: result.enhancedContent
          ? Object.keys(result.enhancedContent)
          : [],
      });

      if (result.success && result.enhancedContent) {
        // Show success feedback to user (same as translation)
        toast.success('Content enhanced successfully!', {
          description: isEnhanced
            ? 'Article has been re-enhanced.'
            : 'Article has been enhanced with AI.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns (same as translation)
        // The API response contains the enhanced content
        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedTitle',
          value: result.enhancedContent.enhancedTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedContent',
          value: result.enhancedContent.enhancedContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedSummary',
          value: result.enhancedContent.summary,
        });

        // Update additional fields if they exist in the response
        if (result.enhancedContent.keyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.enhancedKeyInsights',
            value: result.enhancedContent.keyInsights,
          });
        }

        if (result.enhancedContent.keywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.keywords',
            value: result.enhancedContent.keywords,
          });
        }

        // Set enhancement flag (critical for button state changes)
        dispatchFields({
          type: 'UPDATE',
          path: 'hasBeenEnhanced',
          value: true,
        });

        // Update workflow stage to 'candidate-article'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'candidate-article',
        });

        console.log('✅ Enhancement fields updated successfully');

        // Set immediate visual feedback (same as translation)
        setEnhancementJustCompleted(true);

        // Force form re-render to load updated data from database
        setTimeout(() => {
          console.log('🔄 Refreshing page to load enhanced data...');
          router.refresh();
        }, 1000); // Shorter delay since we're not updating form state manually

        // Reset the completion flag after refresh
        setTimeout(() => {
          setEnhancementJustCompleted(false);
        }, 2000);
      } else {
        console.error('❌ Enhancement failed:', result.error);
        toast.error('Enhancement failed', {
          description: result.error || 'Unknown error during enhancement',
        });
      }
    } catch (error) {
      console.error('❌ Enhancement error:', error);
      toast.error('Enhancement failed', {
        description: 'Failed to communicate with enhancement service',
      });
    } finally {
      setIsEnhancing(false);
    }
  }, [
    id,
    allFieldsValid,
    isEnhanced,
    getValidationMessage,
    dispatchFields,
    toast,
    router,
  ]);

  // Show for both generated and curated articles with appropriate workflow stages
  const validArticleTypes = ['generated', 'curated'];
  const validWorkflowStages = [
    'curated-draft', // New stage for curated articles
    'candidate-article',
    'translated',
    'ready-for-review',
    'published',
  ];

  if (
    !validArticleTypes.includes(articleType) ||
    !validWorkflowStages.includes(workflowStage)
  ) {
    return null;
  }

  // Determine button states
  // For curated articles: both buttons use same disabled logic for consistency
  // For generated articles: maintain existing logic
  const isTranslationDisabled =
    articleType === 'curated'
      ? !allFieldsValid ||
        isTranslating ||
        isEnhancing ||
        translationJustCompleted ||
        enhancementJustCompleted
      : !canTranslate || isTranslating || translationJustCompleted;

  const isEnhancementDisabled =
    articleType === 'curated'
      ? !allFieldsValid ||
        isEnhancing ||
        isTranslating ||
        enhancementJustCompleted ||
        translationJustCompleted
      : !canEnhance || isEnhancing || enhancementJustCompleted;

  // Enhancement button helper functions
  const getEnhanceButtonText = () => {
    if (isEnhancing) return 'Enhancing Content...';
    if (enhancementJustCompleted) return 'Enhancement Complete! Refreshing...';
    if (isEnhanced) return 'Re-enhance Content';
    return 'Enhance Content';
  };

  const getEnhanceButtonColor = () => {
    if (isEnhancing) return '#6B7280'; // Gray for loading
    if (enhancementJustCompleted) return '#10B981'; // Bright green for success
    if (isEnhanced) return '#8B5CF6'; // Purple for re-enhancement
    return '#3B82F6'; // Blue for first enhancement
  };

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    if (hasGermanTranslation) return 'Re-Translate to German';
    return 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  return (
    <div
      className="flex gap-4 items-center mb-4"
      style={{
        marginBottom: 'var(--base, 1rem)',
        gap: 'var(--base-half, 0.5rem)', // Ensure proper spacing between buttons
      }}
    >
      {/* Enhancement Button - Only show for curated articles in draft stage */}
      {showEnhanceButton && (
        <button
          onClick={handleEnhance}
          disabled={isEnhancementDisabled}
          title={
            !allFieldsValid
              ? getValidationMessage() || 'Please complete all required fields'
              : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getEnhanceButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isEnhancementDisabled ? 'not-allowed' : 'pointer',
            opacity: isEnhancementDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isEnhancing && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {enhancementJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getEnhanceButtonText()}</span>
        </button>
      )}

      {/* Translation Button - Only show when conditions are met */}
      {showTranslationButton && (
        <button
          onClick={handleTranslateToGerman}
          disabled={isTranslationDisabled}
          title={
            articleType === 'curated' && !allFieldsValid
              ? getValidationMessage() || 'Please complete all required fields'
              : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getTranslationButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
            opacity: isTranslationDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isTranslating && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {translationJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getTranslationButtonText()}</span>
        </button>
      )}

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default ArticleDocumentControls;
